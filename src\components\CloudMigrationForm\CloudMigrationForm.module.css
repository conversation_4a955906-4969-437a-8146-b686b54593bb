@value variables: "@styles/variables.module.css";
@value brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, colorWhite, colorBlack, gray300 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-xl, breakpoint-xl-1024, breakpoint-xl-1440 from breakpoints;

.form_container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 40px 20px;
  background-color: #f8f9fa;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form_heading {
  text-align: center;
  margin-bottom: 40px;
}

.form_heading h3 {
  font-size: 28px;
  font-weight: 600;
  color: colorBlack;
  margin-bottom: 12px;
}

.form_heading p {
  font-size: 16px;
  color: #666;
  line-height: 1.5;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form_row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;

  @media screen and (max-width: breakpoint-xl-1024) {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

.form_field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form_field label {
  font-size: 14px;
  font-weight: 600;
  color: colorBlack;
}

.form_field input {
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
  background-color: colorWhite;
}

.form_field input:focus {
  outline: none;
  border-color: brandColorOne;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form_field input.error {
  border-color: #ff5656;
}

.error_text {
  font-size: 12px;
  color: #ff5656;
  font-weight: 500;
}

.consent_field {
  margin: 20px 0;
}

.consent_label {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  cursor: pointer;
}

.consent_label input[type="checkbox"] {
  margin-top: 2px;
  width: 18px;
  height: 18px;
  accent-color: brandColorOne;
}

.consent_text {
  font-size: 14px;
  line-height: 1.5;
  color: #666;
}

.submit_section {
  text-align: center;
  margin-top: 20px;
}

.submit_button {
  background: linear-gradient(135deg, brandColorOne 0%, brandColorTwo 100%);
  color: colorWhite;
  border: none;
  padding: 16px 40px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 200px;
}

.submit_button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
}

.submit_button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

@media screen and (max-width: breakpoint-xl-1024) {
  .form_container {
    padding: 24px 16px;
  }
  
  .form_heading h3 {
    font-size: 24px;
  }
  
  .form_heading p {
    font-size: 14px;
  }
  
  .submit_button {
    padding: 14px 32px;
    font-size: 14px;
    min-width: 180px;
  }
}
