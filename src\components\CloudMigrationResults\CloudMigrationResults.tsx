'use client';

import { useRouter } from 'next/navigation';
import Button from '@components/Button';
import Heading from '@components/Heading';
import styles from './CloudMigrationResults.module.css';

export default function CloudMigrationResults({ result, body, handleRestart }) {
  const router = useRouter();

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className={styles.results_wrapper}>
      <div className={styles.results_header}>
        <Heading
          title="Your Cloud Migration Cost Estimate"
          headingType="h2"
          className={styles.main_heading}
        />
        <p className={styles.subtitle}>
          Based on your responses, here's your estimated cloud migration cost range
        </p>
      </div>

      <div className={styles.cost_display}>
        <div className={styles.cost_range_container}>
          <div className={styles.cost_item}>
            <span className={styles.cost_label}>Lower Range</span>
            <span className={styles.cost_value}>
              {formatCurrency(result.lowerRange)}
            </span>
          </div>
          <div className={styles.cost_separator}>-</div>
          <div className={styles.cost_item}>
            <span className={styles.cost_label}>Upper Range</span>
            <span className={styles.cost_value}>
              {formatCurrency(result.upperRange)}
            </span>
          </div>
        </div>
        
        <div className={styles.cost_note}>
          <p>
            <strong>Note:</strong> The upper range is 30% higher than the lower range to account for potential additional requirements and contingencies.
          </p>
        </div>
      </div>

      <div className={styles.cost_breakdown}>
        <Heading
          title="Cost Breakdown Factors"
          headingType="h3"
          className={styles.breakdown_heading}
        />
        
        <div className={styles.breakdown_grid}>
          {result.costFactors.serverCount > 0 && (
            <div className={styles.breakdown_item}>
              <span className={styles.factor_name}>Server Infrastructure</span>
              <span className={styles.factor_cost}>
                {formatCurrency(result.costFactors.serverCount)}
              </span>
            </div>
          )}
          
          {result.costFactors.dataCapacity > 0 && (
            <div className={styles.breakdown_item}>
              <span className={styles.factor_name}>Data Storage & Migration</span>
              <span className={styles.factor_cost}>
                {formatCurrency(result.costFactors.dataCapacity)}
              </span>
            </div>
          )}
          
          {result.costFactors.environments > 0 && (
            <div className={styles.breakdown_item}>
              <span className={styles.factor_name}>Environment Setup</span>
              <span className={styles.factor_cost}>
                {formatCurrency(result.costFactors.environments)}
              </span>
            </div>
          )}
          
          {result.costFactors.compliance > 0 && (
            <div className={styles.breakdown_item}>
              <span className={styles.factor_name}>Compliance & Security</span>
              <span className={styles.factor_cost}>
                {formatCurrency(result.costFactors.compliance)}
              </span>
            </div>
          )}
          
          {result.costFactors.migrationStrategy > 0 && (
            <div className={styles.breakdown_item}>
              <span className={styles.factor_name}>Migration Strategy</span>
              <span className={styles.factor_cost}>
                {formatCurrency(result.costFactors.migrationStrategy)}
              </span>
            </div>
          )}
          
          {result.costFactors.autoScaling > 0 && (
            <div className={styles.breakdown_item}>
              <span className={styles.factor_name}>Auto-scaling Setup</span>
              <span className={styles.factor_cost}>
                {formatCurrency(result.costFactors.autoScaling)}
              </span>
            </div>
          )}
          
          {result.costFactors.highAvailability > 0 && (
            <div className={styles.breakdown_item}>
              <span className={styles.factor_name}>High Availability (20% premium)</span>
              <span className={styles.factor_cost}>
                {formatCurrency((result.costFactors.serverCount + result.costFactors.dataCapacity) * 0.2)}
              </span>
            </div>
          )}
        </div>
      </div>

      <div className={styles.description}>
        <p>
          This estimate provides a ballpark figure based on your current infrastructure, applications, and business needs. 
          The actual cost may vary depending on specific requirements, chosen cloud provider pricing, and implementation complexity.
        </p>
        <p>
          <strong>Next Steps:</strong> Our cloud migration experts can provide a detailed assessment and customized migration plan 
          tailored to your specific requirements.
        </p>
      </div>

      <div className={styles.action_buttons}>
        <Button
          className={styles.consultation_button}
          label={body?.consultation_button?.title || "Get Detailed Consultation"}
          type="button"
          onClick={() => {
            router.push(body?.consultation_button?.link || '/contact-us');
          }}
        />
        
        <button
          className={styles.restart_button}
          onClick={handleRestart}
          type="button"
        >
          {body?.restart_button?.title || "Start New Calculation"}
        </button>
      </div>

      <div className={styles.disclaimer}>
        <p>
          <strong>Disclaimer:</strong> This is an estimated cost range based on the information provided. 
          Actual migration costs may vary based on specific requirements, chosen cloud provider, 
          implementation complexity, and market conditions. For a detailed quote, please contact our experts.
        </p>
      </div>
    </div>
  );
}
