'use client';

import { useState } from 'react';
import useForm from '@hooks/useForm';
import styles from './CloudMigrationForm.module.css';

export default function CloudMigrationForm({
  formData,
  handleResult,
  handleVisibleSection,
}) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const initialValues = {
    firstName: '',
    lastName: '',
    emailAddress: '',
    companyName: '',
    phoneNumber: '',
    howCanWeHelpYou: '',
    consent: false,
  };

  const initialErrors = {
    firstName: { empty: false, invalid: false },
    lastName: { empty: false, invalid: false },
    emailAddress: { empty: false, invalid: false },
    companyName: { empty: false, invalid: false },
    phoneNumber: { empty: false, invalid: false },
    howCanWeHelpYou: { empty: false, invalid: false },
    consent: { empty: false, invalid: false },
  };

  const {
    values,
    errors,
    errorMessages,
    handleChange,
    handleSubmit: originalHandleSubmit,
  } = useForm(
    initialValues,
    initialErrors,
    'cloudMigration',
    'Cloud Migration Cost Calculator',
  );

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Calculate the migration cost first
      const calculationResult = handleResult();
      
      if (calculationResult) {
        // Prepare form data with calculation results
        const formDataWithResults = {
          ...values,
          ...calculationResult.newResult,
          calculationData: calculationResult.data,
          secondary_source: 'Cloud Migration Cost Calculator',
        };

        // Submit the form
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_SITE_URL}/api/cloud-migration-cost-calculator/`,
          {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(formDataWithResults),
          },
        );

        if (response.ok) {
          // Move to results section
          handleVisibleSection(999); // Move to results
        } else {
          console.error('Error submitting form:', await response.json());
        }
      }
    } catch (error) {
      console.error('Error in form submission:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={styles.form_container}>
      <div className={styles.form_heading}>
        <h3>{formData?.title || 'Contact Details'}</h3>
        <p>{formData?.instructions || 'Please provide your contact information to receive your cloud migration cost estimate.'}</p>
      </div>
      
      <form onSubmit={handleSubmit} className={styles.form}>
        <div className={styles.form_row}>
          <div className={styles.form_field}>
            <label htmlFor="firstName">First Name*</label>
            <input
              type="text"
              id="firstName"
              name="firstName"
              value={values.firstName}
              onChange={(e) => handleChange({
                name: e.target.name,
                value: e.target.value,
              })}
              className={errors.firstName.empty || errors.firstName.invalid ? styles.error : ''}
              required
            />
            {(errors.firstName.empty || errors.firstName.invalid) && (
              <span className={styles.error_text}>
                {errors.firstName.empty ? 'First name is required' : 'Invalid first name'}
              </span>
            )}
          </div>
          
          <div className={styles.form_field}>
            <label htmlFor="lastName">Last Name*</label>
            <input
              type="text"
              id="lastName"
              name="lastName"
              value={values.lastName}
              onChange={(e) => handleChange({
                name: e.target.name,
                value: e.target.value,
              })}
              className={errors.lastName.empty || errors.lastName.invalid ? styles.error : ''}
              required
            />
            {(errors.lastName.empty || errors.lastName.invalid) && (
              <span className={styles.error_text}>
                {errors.lastName.empty ? 'Last name is required' : 'Invalid last name'}
              </span>
            )}
          </div>
        </div>

        <div className={styles.form_row}>
          <div className={styles.form_field}>
            <label htmlFor="emailAddress">Email Address*</label>
            <input
              type="email"
              id="emailAddress"
              name="emailAddress"
              value={values.emailAddress}
              onChange={(e) => handleChange({
                name: e.target.name,
                value: e.target.value,
              })}
              className={errors.emailAddress.empty || errors.emailAddress.invalid ? styles.error : ''}
              required
            />
            {(errors.emailAddress.empty || errors.emailAddress.invalid) && (
              <span className={styles.error_text}>
                {errors.emailAddress.empty ? 'Email is required' : 'Invalid email address'}
              </span>
            )}
          </div>
          
          <div className={styles.form_field}>
            <label htmlFor="companyName">Company Name*</label>
            <input
              type="text"
              id="companyName"
              name="companyName"
              value={values.companyName}
              onChange={(e) => handleChange({
                name: e.target.name,
                value: e.target.value,
              })}
              className={errors.companyName.empty || errors.companyName.invalid ? styles.error : ''}
              required
            />
            {(errors.companyName.empty || errors.companyName.invalid) && (
              <span className={styles.error_text}>
                {errors.companyName.empty ? 'Company name is required' : 'Invalid company name'}
              </span>
            )}
          </div>
        </div>

        <div className={styles.form_row}>
          <div className={styles.form_field}>
            <label htmlFor="phoneNumber">Phone Number*</label>
            <input
              type="tel"
              id="phoneNumber"
              name="phoneNumber"
              value={values.phoneNumber}
              onChange={(e) => handleChange({
                name: e.target.name,
                value: e.target.value,
              })}
              className={errors.phoneNumber.empty || errors.phoneNumber.invalid ? styles.error : ''}
              required
            />
            {(errors.phoneNumber.empty || errors.phoneNumber.invalid) && (
              <span className={styles.error_text}>
                {errors.phoneNumber.empty ? 'Phone number is required' : 'Invalid phone number'}
              </span>
            )}
          </div>
          
          <div className={styles.form_field}>
            <label htmlFor="howCanWeHelpYou">How Can We Help You?</label>
            <input
              type="text"
              id="howCanWeHelpYou"
              name="howCanWeHelpYou"
              value={values.howCanWeHelpYou}
              onChange={(e) => handleChange({
                name: e.target.name,
                value: e.target.value,
              })}
            />
          </div>
        </div>

        <div className={styles.consent_field}>
          <label className={styles.consent_label}>
            <input
              type="checkbox"
              name="consent"
              checked={values.consent}
              onChange={(e) => handleChange({
                name: e.target.name,
                value: e.target.checked,
                type: 'checkbox',
                checked: e.target.checked,
              })}
              required
            />
            <span className={styles.consent_text}>
              I consent to processing of my personal data entered above for Maruti Techlabs to contact me.*
            </span>
          </label>
          {errors.consent.empty && (
            <span className={styles.error_text}>
              You must consent to processing of your personal data
            </span>
          )}
        </div>

        <div className={styles.submit_section}>
          <button
            type="submit"
            className={styles.submit_button}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Calculating...' : formData?.button?.title || 'Get My Cost Estimate'}
          </button>
        </div>
      </form>
    </div>
  );
}
