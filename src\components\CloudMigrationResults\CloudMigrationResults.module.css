@value variables: "@styles/variables.module.css";
@value brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, colorWhite, colorBlack, gray300 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-xl, breakpoint-xl-1024, breakpoint-xl-1440 from breakpoints;

.results_wrapper {
  width: fit-content;
  display: flex;
  flex-direction: column;
  gap: 40px;
  margin: 0 auto;
  padding: 40px 20px;
  text-align: center;

  @media screen and (min-width: 1200px) {
    width: 1192px;
  }
}

.results_header {
  margin-bottom: 20px;
}

.main_heading {
  font-size: 36px;
  font-weight: 700;
  color: colorBlack;
  margin-bottom: 16px;

  @media screen and (max-width: breakpoint-xl-1024) {
    font-size: 28px;
  }
}

.subtitle {
  font-size: 18px;
  color: #666;
  line-height: 1.6;

  @media screen and (max-width: breakpoint-xl-1024) {
    font-size: 16px;
  }
}

.cost_display {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: colorWhite;
  padding: 40px;
  border-radius: 16px;
  margin: 20px 0;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);

  @media screen and (max-width: breakpoint-xl-1024) {
    padding: 24px;
    margin: 16px 0;
  }
}

.cost_range_container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 40px;
  margin-bottom: 24px;

  @media screen and (max-width: breakpoint-xl-1024) {
    flex-direction: column;
    gap: 20px;
  }
}

.cost_item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.cost_label {
  font-size: 16px;
  font-weight: 500;
  opacity: 0.9;
}

.cost_value {
  font-size: 48px;
  font-weight: 700;
  line-height: 1;

  @media screen and (max-width: breakpoint-xl-1024) {
    font-size: 36px;
  }
}

.cost_separator {
  font-size: 36px;
  font-weight: 300;
  opacity: 0.8;

  @media screen and (max-width: breakpoint-xl-1024) {
    display: none;
  }
}

.cost_note {
  background-color: rgba(255, 255, 255, 0.1);
  padding: 16px;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.5;
}

.cost_breakdown {
  background-color: #f8f9fa;
  padding: 32px;
  border-radius: 12px;
  text-align: left;

  @media screen and (max-width: breakpoint-xl-1024) {
    padding: 20px;
  }
}

.breakdown_heading {
  font-size: 24px;
  font-weight: 600;
  color: colorBlack;
  margin-bottom: 24px;
  text-align: center;

  @media screen and (max-width: breakpoint-xl-1024) {
    font-size: 20px;
    margin-bottom: 20px;
  }
}

.breakdown_grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;

  @media screen and (max-width: breakpoint-xl-1024) {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

.breakdown_item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: colorWhite;
  border-radius: 8px;
  border-left: 4px solid brandColorOne;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.factor_name {
  font-size: 16px;
  font-weight: 500;
  color: colorBlack;
}

.factor_cost {
  font-size: 18px;
  font-weight: 700;
  color: brandColorOne;
}

.description {
  background-color: #f0f4f8;
  padding: 24px;
  border-radius: 12px;
  text-align: left;
  line-height: 1.6;

  @media screen and (max-width: breakpoint-xl-1024) {
    padding: 20px;
  }
}

.description p {
  margin-bottom: 16px;
  font-size: 16px;
  color: #333;
}

.description p:last-child {
  margin-bottom: 0;
}

.action_buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 32px 0;

  @media screen and (max-width: breakpoint-xl-1024) {
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }
}

.consultation_button {
  background: linear-gradient(135deg, brandColorOne 0%, brandColorTwo 100%);
  color: colorWhite;
  border: none;
  padding: 16px 32px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.consultation_button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
}

.restart_button {
  background-color: transparent;
  color: brandColorOne;
  border: 2px solid brandColorOne;
  padding: 14px 30px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.restart_button:hover {
  background-color: brandColorOne;
  color: colorWhite;
  transform: translateY(-2px);
}

.disclaimer {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  padding: 20px;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.5;
  color: #856404;
  text-align: left;

  @media screen and (max-width: breakpoint-xl-1024) {
    padding: 16px;
    font-size: 13px;
  }
}

.disclaimer p {
  margin: 0;
}
